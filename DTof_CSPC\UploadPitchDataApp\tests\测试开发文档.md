# MES数据上传功能测试开发文档

## 1. 测试程序概述

### 1.1 开发目标
- 开发完整的MES数据上传功能测试程序
- 使用模拟信号模拟正常的上传MES数据功能
- 编译器与主程序保持一致
- 提供自动化测试和手动验证功能

### 1.2 测试程序架构
```
tests/
├── TestDataGenerator.h/cpp     # 测试数据生成器
├── MockMESUploader.h/cpp       # 模拟MES上传器
├── MESSimulationTest.cpp       # 主测试程序
├── MESFunctionTest.cpp         # 原有功能测试
├── CMakeLists.txt              # 构建配置
└── 测试开发文档.md             # 本文档
```

### 1.3 核心组件说明

#### TestDataGenerator（测试数据生成器）
- **功能**: 生成各种测试场景的CSV数据文件
- **支持场景**: 正常数据、重复数据、异常数据、大数据量
- **配置项**: 固件版本、时间范围、角度范围等
- **输出格式**: 标准CSV格式，与实际数据格式完全一致

#### MockMESUploader（模拟MES上传器）
- **功能**: 模拟真实的MES数据上传过程
- **模拟能力**: 网络延迟、上传成功/失败、数据库异常
- **统计功能**: 上传计数、成功率、失败原因分析
- **信号机制**: 完整的Qt信号槽机制，与真实上传器接口一致

#### MESSimulationTest（主测试类）
- **功能**: 综合测试MES上传功能的各个方面
- **测试覆盖**: 正常流程、异常处理、性能测试、集成测试
- **验证机制**: 使用QSignalSpy验证信号发射和数据处理

## 2. 编译配置

### 2.1 编译器设置
- **C++标准**: C++11（与主程序一致）
- **Qt版本**: Qt5（与主程序一致）
- **编码设置**: UTF-8（与主程序一致）
- **编译选项**: 与主程序保持完全一致

### 2.2 依赖库
```cmake
find_package(Qt5 REQUIRED COMPONENTS Core Widgets Sql Test)
```

### 2.3 构建目标
- **MESFunctionTest**: 原有的功能测试程序
- **MESSimulationTest**: 新开发的模拟测试程序

## 3. 测试用例设计

### 3.1 核心功能测试

#### testNormalMESUploadSimulation()
- **目的**: 测试正常的MES上传流程
- **数据**: 生成5条标准格式的测试数据
- **验证**: 信号发射、上传统计、成功率
- **期望**: 至少80%成功率

#### testDuplicateDataHandling()
- **目的**: 测试重复数据处理机制
- **数据**: 生成包含重复电机标签的数据
- **验证**: 去重逻辑、最新数据保留
- **期望**: 去重后数据量减少，保留最新记录

#### testWorkingCopyManagement()
- **目的**: 测试工作拷贝文件管理
- **验证**: 工作拷贝创建、状态更新、文件格式
- **期望**: 正确创建工作拷贝并更新状态

#### testSignalEmissionVerification()
- **目的**: 验证信号槽机制
- **验证**: uploadStarted、uploadCompleted、statisticsUpdated信号
- **期望**: 信号发射次数与数据量匹配

### 3.2 异常情况测试

#### testNetworkInterruptionSimulation()
- **目的**: 模拟网络中断情况
- **模拟**: 2秒网络中断
- **验证**: 网络中断信号、失败统计
- **期望**: 正确处理网络异常

#### testDatabaseConnectionFailure()
- **目的**: 模拟数据库连接失败
- **验证**: 数据库异常处理、错误记录
- **期望**: 优雅处理数据库异常

#### testInvalidDataHandling()
- **目的**: 测试无效数据处理
- **数据**: 格式错误、缺少列、数据类型错误
- **验证**: 错误检测、异常处理
- **期望**: 不会崩溃，正确记录错误

### 3.3 性能测试

#### testLargeDataSetProcessing()
- **目的**: 测试大数据量处理能力
- **数据**: 100条记录的大数据集
- **验证**: 处理时间、内存使用、成功率
- **期望**: 15秒内完成处理

#### testUploadPerformance()
- **目的**: 测试上传性能
- **验证**: 响应时间、吞吐量
- **期望**: 满足性能要求

## 4. 模拟功能详解

### 4.1 数据生成模拟

#### 正常数据生成
```cpp
QString generateNormalCSV(const QString& fileName, int recordCount = 10);
```
- 生成标准格式的CSV文件
- 包含完整的10列数据
- 随机生成电机标签、MCU ID、角度值等

#### 重复数据生成
```cpp
QString generateDuplicateCSV(const QString& fileName, int duplicateCount = 3);
```
- 生成包含重复电机标签的数据
- 用于测试去重逻辑
- 模拟实际生产中的重复测试情况

#### 异常数据生成
```cpp
QString generateInvalidCSV(const QString& fileName, ExceptionType type);
```
- 支持多种异常类型：格式错误、缺少列、数据类型错误等
- 用于测试异常处理机制

### 4.2 上传过程模拟

#### 成功率控制
```cpp
void setSuccessRate(double rate);  // 0.0-1.0
```
- 可配置的上传成功率
- 模拟真实环境的成功率变化

#### 网络延迟模拟
```cpp
void setNetworkDelay(int minMs, int maxMs);
```
- 模拟网络延迟范围
- 随机生成延迟时间

#### 异常情况模拟
```cpp
void simulateNetworkInterruption(int durationMs);
void simulateDatabaseMaintenance(int durationMs);
void simulateHighLoad(bool enabled);
```
- 网络中断模拟
- 数据库维护模拟
- 高负载情况模拟

### 4.3 统计功能

#### 上传统计
- 总上传数量
- 成功数量
- 失败数量
- 实时成功率

#### 失败原因分析
- 网络错误
- 数据库错误
- 数据验证错误
- 超时错误
- 未知错误

## 5. 编译和运行

### 5.1 编译步骤

1. **进入测试目录**
```bash
cd DTof_CSPC/UploadPitchDataApp/tests
```

2. **创建构建目录**
```bash
mkdir build
cd build
```

3. **配置CMake**
```bash
cmake ..
```

4. **编译**
```bash
make  # Linux/Mac
# 或者在Windows上使用Visual Studio
```

### 5.2 运行测试

#### 运行模拟测试
```bash
./bin/MESSimulationTest
```

#### 运行功能测试
```bash
./bin/MESFunctionTest
```

### 5.3 测试输出

#### 控制台输出示例
```
=== MES模拟测试开始 ===
测试目标：使用模拟信号验证MES上传功能的完整性

--- 测试正常MES上传模拟 ---
生成正常CSV文件:normal_upload_test.csv 记录数:5
✅ 正常CSV文件生成成功
读取到测试数据:5 条
📤 Starting batch data upload, count:5
📊 Upload result: MES_12345678_1642425600000 -> SUCCESS
上传统计 - 总数:5 成功:5 失败:0
✅ 正常MES上传模拟测试通过

--- 测试重复数据处理模拟 ---
原始数据条数:7
去重后数据条数:4
✅ 重复数据处理模拟测试通过

=== MES模拟测试结束 ===
```

## 6. 测试验证标准

### 6.1 功能验证
- ✅ 所有核心测试用例100%通过
- ✅ 信号发射次数与预期一致
- ✅ 数据处理结果正确
- ✅ 工作拷贝管理正常

### 6.2 性能验证
- ✅ 100条记录处理时间<15秒
- ✅ 内存使用合理
- ✅ 95%以上成功率（正常情况下）

### 6.3 异常处理验证
- ✅ 网络中断正确处理
- ✅ 数据库异常优雅处理
- ✅ 无效数据不会导致崩溃
- ✅ 错误信息准确记录

## 7. 扩展和维护

### 7.1 添加新测试用例
1. 在MESSimulationTest类中添加新的测试方法
2. 使用QTEST_*宏进行断言验证
3. 更新测试文档

### 7.2 修改模拟参数
1. 调整TestDataGenerator的配置参数
2. 修改MockMESUploader的模拟行为
3. 根据实际需求调整测试数据

### 7.3 性能优化
1. 监控测试执行时间
2. 优化大数据量处理
3. 改进内存使用效率

---

**文档版本**: V1.0  
**创建日期**: 2025-01-17  
**更新日期**: 2025-01-17  
**开发状态**: 已完成
