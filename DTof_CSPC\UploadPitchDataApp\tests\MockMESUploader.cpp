#include "MockMESUploader.h"

MockMESUploader::MockMESUploader(QObject *parent)
    : QObject(parent)
    , m_successRate(0.95)  // 默认95%成功率
    , m_minNetworkDelay(100)
    , m_maxNetworkDelay(500)
    , m_databaseConnectionFailure(false)
    , m_randomFailureRate(0.02)  // 默认2%随机失败率
    , m_batchUploadMode(false)
    , m_networkInterrupted(false)
    , m_databaseMaintenance(false)
    , m_highLoadSimulation(false)
    , m_totalUploaded(0)
    , m_successCount(0)
    , m_failureCount(0)
{
    // 初始化定时器
    m_uploadTimer = new QTimer(this);
    m_networkRecoveryTimer = new QTimer(this);
    m_databaseRecoveryTimer = new QTimer(this);

    // 连接信号槽
    connect(m_uploadTimer, &QTimer::timeout, this, &MockMESUploader::onUploadTimer);
    connect(m_networkRecoveryTimer, &QTimer::timeout, this, &MockMESUploader::onNetworkRecovery);
    connect(m_databaseRecoveryTimer, &QTimer::timeout, this, &MockMESUploader::onDatabaseRecovery);

    // 设置定时器为单次触发
    m_uploadTimer->setSingleShot(true);
    m_networkRecoveryTimer->setSingleShot(true);
    m_databaseRecoveryTimer->setSingleShot(true);

    qDebug() << "MockMESUploader initialized with success rate:" << m_successRate;
}

MockMESUploader::~MockMESUploader()
{
    qDebug() << "MockMESUploader destroyed. Final statistics:";
    qDebug() << "- Total uploaded:" << m_totalUploaded;
    qDebug() << "- Success count:" << m_successCount;
    qDebug() << "- Failure count:" << m_failureCount;
    qDebug() << "- Success rate:" << getCurrentSuccessRate();
}

void MockMESUploader::setSuccessRate(double rate)
{
    m_successRate = qBound(0.0, rate, 1.0);
    qDebug() << "Success rate set to:" << m_successRate;
}

void MockMESUploader::setNetworkDelay(int minMs, int maxMs)
{
    m_minNetworkDelay = qMax(0, minMs);
    m_maxNetworkDelay = qMax(m_minNetworkDelay, maxMs);
    qDebug() << "Network delay range set to:" << m_minNetworkDelay << "-" << m_maxNetworkDelay << "ms";
}

void MockMESUploader::setDatabaseConnectionFailure(bool enabled)
{
    m_databaseConnectionFailure = enabled;
    qDebug() << "Database connection failure simulation:" << (enabled ? "enabled" : "disabled");
}

void MockMESUploader::setRandomFailureRate(double rate)
{
    m_randomFailureRate = qBound(0.0, rate, 1.0);
    qDebug() << "Random failure rate set to:" << m_randomFailureRate;
}

void MockMESUploader::setBatchUploadMode(bool enabled)
{
    m_batchUploadMode = enabled;
    qDebug() << "Batch upload mode:" << (enabled ? "enabled" : "disabled");
}

void MockMESUploader::uploadSingleData(const MESData& data)
{
    QString identifier = generateUploadIdentifier(data);
    qDebug() << "📤 Starting single data upload:" << identifier;

    emit uploadStarted(identifier);

    // 计算延迟并安排上传
    int delay = calculateDelay();
    UploadTask task;
    task.identifier = identifier;
    task.scheduledTime = QDateTime::currentDateTime().addMSecs(delay);
    task.retryCount = 0;

    m_uploadQueue.append(task);

    // 启动定时器
    m_uploadTimer->start(delay);
}

void MockMESUploader::uploadBatchData(const QVector<MESData>& dataList)
{
    qDebug() << "📤 Starting batch data upload, count:" << dataList.size();

    if (dataList.isEmpty()) {
        emit batchUploadCompleted(0, 0, 0);
        return;
    }

    int totalCount = dataList.size();
    int successCount = 0;
    int failureCount = 0;

    for (int i = 0; i < dataList.size(); ++i) {
        const MESData& data = dataList[i];
        QString identifier = generateUploadIdentifier(data);

        emit uploadStarted(identifier);
        emit uploadProgress(i + 1, totalCount);

        // 模拟上传
        UploadResult result = simulateUpload(identifier);
        updateStatistics(result);

        if (result == UploadResult::Success) {
            successCount++;
        } else {
            failureCount++;
        }

        emit uploadCompleted(identifier, result);

        // 批量模式下添加小延迟
        if (m_batchUploadMode && i < dataList.size() - 1) {
            QThread::msleep(10);  // 10ms延迟
        }
    }

    emit batchUploadCompleted(totalCount, successCount, failureCount);
    emit statisticsUpdated(m_totalUploaded, m_successCount, m_failureCount);

    qDebug() << "✅ Batch upload completed. Success:" << successCount << "Failure:" << failureCount;
}

void MockMESUploader::uploadPitchData(const QVector<PitchData>& pitchDataList)
{
    qDebug() << "📤 Starting PitchData upload, count:" << pitchDataList.size();

    // 将PitchData转换为MESData（简化版本）
    QVector<MESData> mesDataList;
    for (const PitchData& pitchData : pitchDataList) {
        MESData mesData;
        mesData.nbr = pitchData.getNbr();
        mesData.mcuID = pitchData.getMcuID();
        mesData.testResult = pitchData.getTestResult();
        mesData.act1 = pitchData.getPitchAngleValueMin();
        mesData.act2 = pitchData.getPitchAngleValueMax();
        mesData.date = QDate::currentDate().toString("yyyy/MM/dd");
        mesData.time = QTime::currentTime().msecsSinceStartOfDay() / 1000;

        mesDataList.append(mesData);
    }

    // 使用批量上传
    uploadBatchData(mesDataList);
}

int MockMESUploader::getTotalUploaded() const
{
    return m_totalUploaded;
}

int MockMESUploader::getSuccessCount() const
{
    return m_successCount;
}

int MockMESUploader::getFailureCount() const
{
    return m_failureCount;
}

double MockMESUploader::getCurrentSuccessRate() const
{
    if (m_totalUploaded == 0) {
        return 0.0;
    }
    return static_cast<double>(m_successCount) / m_totalUploaded;
}

QStringList MockMESUploader::getFailureReasons() const
{
    return m_failureReasons;
}

void MockMESUploader::resetStatistics()
{
    m_totalUploaded = 0;
    m_successCount = 0;
    m_failureCount = 0;
    m_failureReasons.clear();
    m_uploadQueue.clear();

    qDebug() << "Statistics reset";
    emit statisticsUpdated(m_totalUploaded, m_successCount, m_failureCount);
}

void MockMESUploader::simulateNetworkInterruption(int durationMs)
{
    qDebug() << "🔌 Simulating network interruption for" << durationMs << "ms";
    
    m_networkInterrupted = true;
    emit networkInterrupted();
    
    m_networkRecoveryTimer->start(durationMs);
}

void MockMESUploader::simulateDatabaseMaintenance(int durationMs)
{
    qDebug() << "🔧 Simulating database maintenance for" << durationMs << "ms";
    
    m_databaseMaintenance = true;
    emit databaseConnectionLost();
    
    m_databaseRecoveryTimer->start(durationMs);
}

void MockMESUploader::simulateHighLoad(bool enabled)
{
    m_highLoadSimulation = enabled;
    qDebug() << "⚡ High load simulation:" << (enabled ? "enabled" : "disabled");
    
    if (enabled) {
        // 高负载时增加延迟
        setNetworkDelay(m_minNetworkDelay * 3, m_maxNetworkDelay * 3);
        setRandomFailureRate(m_randomFailureRate * 2);
    } else {
        // 恢复正常延迟
        setNetworkDelay(100, 500);
        setRandomFailureRate(0.02);
    }
}

// 私有槽函数
void MockMESUploader::onUploadTimer()
{
    if (m_uploadQueue.isEmpty()) {
        return;
    }

    // 处理队列中的第一个任务
    UploadTask task = m_uploadQueue.takeFirst();
    UploadResult result = simulateUpload(task.identifier);
    
    updateStatistics(result);
    emit uploadCompleted(task.identifier, result);
    emit statisticsUpdated(m_totalUploaded, m_successCount, m_failureCount);

    // 如果还有任务，继续处理
    if (!m_uploadQueue.isEmpty()) {
        int delay = calculateDelay();
        m_uploadTimer->start(delay);
    }
}

void MockMESUploader::onNetworkRecovery()
{
    m_networkInterrupted = false;
    qDebug() << "🔌 Network recovered";
}

void MockMESUploader::onDatabaseRecovery()
{
    m_databaseMaintenance = false;
    qDebug() << "🔧 Database maintenance completed";
}

// 私有辅助方法
MockMESUploader::UploadResult MockMESUploader::simulateUpload(const QString& identifier)
{
    // 检查网络中断
    if (m_networkInterrupted) {
        logUploadResult(identifier, UploadResult::NetworkError);
        return UploadResult::NetworkError;
    }

    // 检查数据库维护
    if (m_databaseMaintenance || m_databaseConnectionFailure) {
        logUploadResult(identifier, UploadResult::DatabaseError);
        return UploadResult::DatabaseError;
    }

    // 随机失败检查
    double randomValue = QRandomGenerator::global()->generateDouble();
    if (randomValue < m_randomFailureRate) {
        // 随机选择一个失败类型
        QVector<UploadResult> failureTypes = {
            UploadResult::NetworkError,
            UploadResult::DatabaseError,
            UploadResult::DataValidationError,
            UploadResult::TimeoutError,
            UploadResult::UnknownError
        };
        
        int index = QRandomGenerator::global()->bounded(failureTypes.size());
        UploadResult result = failureTypes[index];
        logUploadResult(identifier, result);
        return result;
    }

    // 成功率检查
    if (randomValue < m_successRate) {
        logUploadResult(identifier, UploadResult::Success);
        return UploadResult::Success;
    }

    // 默认失败
    logUploadResult(identifier, UploadResult::UnknownError);
    return UploadResult::UnknownError;
}

int MockMESUploader::calculateDelay()
{
    int baseDelay = QRandomGenerator::global()->bounded(m_minNetworkDelay, m_maxNetworkDelay + 1);
    
    // 高负载时增加延迟
    if (m_highLoadSimulation) {
        baseDelay *= 2;
    }
    
    return baseDelay;
}

QString MockMESUploader::generateUploadIdentifier(const MESData& data)
{
    return QString("MES_%1_%2").arg(data.nbr).arg(QDateTime::currentMSecsSinceEpoch());
}

QString MockMESUploader::generateUploadIdentifier(const PitchData& data)
{
    return QString("PITCH_%1_%2").arg(data.getNbr()).arg(QDateTime::currentMSecsSinceEpoch());
}

void MockMESUploader::updateStatistics(UploadResult result)
{
    m_totalUploaded++;
    
    if (result == UploadResult::Success) {
        m_successCount++;
    } else {
        m_failureCount++;
    }
}

void MockMESUploader::logUploadResult(const QString& identifier, UploadResult result)
{
    QString resultStr;
    switch (result) {
    case UploadResult::Success:
        resultStr = "SUCCESS";
        break;
    case UploadResult::NetworkError:
        resultStr = "NETWORK_ERROR";
        m_failureReasons.append("Network Error");
        break;
    case UploadResult::DatabaseError:
        resultStr = "DATABASE_ERROR";
        m_failureReasons.append("Database Error");
        break;
    case UploadResult::DataValidationError:
        resultStr = "DATA_VALIDATION_ERROR";
        m_failureReasons.append("Data Validation Error");
        break;
    case UploadResult::TimeoutError:
        resultStr = "TIMEOUT_ERROR";
        m_failureReasons.append("Timeout Error");
        break;
    case UploadResult::UnknownError:
    default:
        resultStr = "UNKNOWN_ERROR";
        m_failureReasons.append("Unknown Error");
        break;
    }
    
    qDebug() << "📊 Upload result:" << identifier << "->" << resultStr;
}
