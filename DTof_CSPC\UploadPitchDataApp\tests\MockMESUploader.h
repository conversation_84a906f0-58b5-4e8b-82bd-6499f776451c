#ifndef MOCKMESUPLOADER_H
#define MOCKMESUPLOADER_H

#include "../Pojo/MESData.h"
#include "../Pojo/PitchData.h"
#include <QDateTime>
#include <QDebug>
#include <QObject>
#include <QRandomGenerator>
#include <QThread>
#include <QTimer>
#include <QVector>

/**
 * @brief 模拟MES上传器类
 *
 * 用于模拟真实的MES数据上传过程，包括：
 * - 模拟网络延迟
 * - 模拟上传成功/失败
 * - 模拟数据库连接异常
 * - 统计上传结果
 * - 提供可配置的成功率和延迟
 */
class MockMESUploader : public QObject {
    Q_OBJECT

  public:
    explicit MockMESUploader(QObject *parent = nullptr);
    ~MockMESUploader();

    // 上传结果枚举
    enum class UploadResult {
        Success,              // 上传成功
        NetworkError,         // 网络错误
        DatabaseError,        // 数据库错误
        DataValidationError,  // 数据验证错误
        TimeoutError,         // 超时错误
        UnknownError          // 未知错误
    };

    // 配置方法
    void setSuccessRate(double rate);                 // 设置成功率 (0.0-1.0)
    void setNetworkDelay(int minMs, int maxMs);       // 设置网络延迟范围
    void setDatabaseConnectionFailure(bool enabled);  // 设置数据库连接失败模拟
    void setRandomFailureRate(double rate);           // 设置随机失败率
    void setBatchUploadMode(bool enabled);            // 设置批量上传模式

    // 上传方法
    void uploadSingleData(const MESData &data);
    void uploadBatchData(const QVector<MESData> &dataList);
    void uploadPitchData(const QVector<PitchData> &pitchDataList);

    // 状态查询
    int         getTotalUploaded() const;
    int         getSuccessCount() const;
    int         getFailureCount() const;
    double      getCurrentSuccessRate() const;
    QStringList getFailureReasons() const;

    // 重置统计
    void resetStatistics();

    // 模拟控制
    void simulateNetworkInterruption(int durationMs);
    void simulateDatabaseMaintenance(int durationMs);
    void simulateHighLoad(bool enabled);

  signals:
    // 上传结果信号
    void uploadStarted(const QString &identifier);
    void uploadCompleted(const QString &identifier, UploadResult result);
    void uploadProgress(int current, int total);

    // 统计信号
    void statisticsUpdated(int total, int success, int failure);
    void batchUploadCompleted(int totalCount, int successCount, int failureCount);

    // 异常信号
    void networkInterrupted();
    void databaseConnectionLost();
    void uploadTimeout(const QString &identifier);

  private slots:
    void onUploadTimer();
    void onNetworkRecovery();
    void onDatabaseRecovery();

  private:
    // 私有辅助方法
    UploadResult simulateUpload(const QString &identifier);
    int          calculateDelay();
    QString      generateUploadIdentifier(const MESData &data);
    QString      generateUploadIdentifier(const PitchData &data);
    void         updateStatistics(UploadResult result);
    void         logUploadResult(const QString &identifier, UploadResult result);

    // 配置参数
    double m_successRate;                // 成功率
    int    m_minNetworkDelay;            // 最小网络延迟(ms)
    int    m_maxNetworkDelay;            // 最大网络延迟(ms)
    bool   m_databaseConnectionFailure;  // 数据库连接失败模拟
    double m_randomFailureRate;          // 随机失败率
    bool   m_batchUploadMode;            // 批量上传模式

    // 状态管理
    bool m_networkInterrupted;   // 网络中断状态
    bool m_databaseMaintenance;  // 数据库维护状态
    bool m_highLoadSimulation;   // 高负载模拟

    // 统计数据
    int         m_totalUploaded;   // 总上传数
    int         m_successCount;    // 成功数
    int         m_failureCount;    // 失败数
    QStringList m_failureReasons;  // 失败原因列表

    // 定时器
    QTimer *m_uploadTimer;            // 上传定时器
    QTimer *m_networkRecoveryTimer;   // 网络恢复定时器
    QTimer *m_databaseRecoveryTimer;  // 数据库恢复定时器

    // 待上传队列
    struct UploadTask {
        QString   identifier;
        QDateTime scheduledTime;
        int       retryCount;
    };
    QVector<UploadTask> m_uploadQueue;
};

#endif  // MOCKMESUPLOADER_H
