
typedef struct ODBC_XSUB
{
	CString softwaveVersion;
	CString domain;//域名
	CString work_order;//工单
	bool supersuser;
	long trnbr;//事务号 每笔需要自加1
	CString nbr;//产品编号
	CString top_nbr;//产品编号
	CString op;//工位号
	CString pre_op;
	CString station;//工位站点
	CString userid;//操作员
	CString user_type;//账号类型
	CString user_name;
	short dateYear;//操作日期 年
	short dateMonth;//操作日期 月
	short dateDay;//操作日期 日
	long time;//操作时间
	bool rslt;//是否合格
	CString rsn_code;//原因代码（不良原因）
	CString project[3];//测试项
	CString stand[3];//标准值
	CString act[3];//实测值
	bool hege[3];
	CString TopMcuIdStr;
	unsigned char TopMcuId[12];

	CString QRcode;//系统SN码
	CString QRcodeLidar;//雷达存储的产品编码
	char QRcodeBuf[21];//产品编号buf
	CString cur_work_order;//系统SN码
	CString sys_correlation_nbr;//系统SN码
	CString LidarTopVersionStr;
    CString LidarBottomVersionStr;

}ODBC_Xsub_st;
result_t CCoordinateDisplayDlg::ReadODBCDataQRcode(CString nbr)//可查询当前标签以关联的标签
{
	CString strVal;
	CString strVal_1;
	result_t read_QRcode_flag = RESULT_MES_QRcode_NG;
	try {
		m_odbc.Open(NULL, false, false, _T(DATABASE_OPEN_SQL), true);
		if (m_odbc.IsOpen())
		{
			rs.m_pDatabase = &m_odbc;
			CString str_tmp;
			//国科、和奕华域判断 0~59999999 和奕华域  60000000~99999999国科域
			if (atoi(nbr) >= 60000000)
			{

				str_tmp.Format(_T("select * from pub.xsub0_det where xsub0_domain = '001' and xsub0_nbr = '%s' with(nolock)"), nbr);
			}
			else
			{
				str_tmp.Format(_T("select * from pub.xsub0_det where xsub0_domain = '003' and xsub0_nbr = '%s' with(nolock)"), nbr);
			}

			rs.Open(CRecordset::forwardOnly, _T(str_tmp));//查找最大值
			if (rs.IsOpen())
			{
				long temp = 0;

				while (!rs.IsEOF())
				{
					rs.GetFieldValue((short)2, strVal);//xsub0_source所在的位置 关联码
					if (strVal.GetLength() == 21)
					{
						read_QRcode_flag = RESULT_MES_QRcode_OK;

					}
					else
					{
						read_QRcode_flag = RESULT_MES_QRcode_NG;
					}
					rs.GetFieldValue((short)3, Xsub6.cur_work_order);//工单xsub0__chr01
					rs.GetFieldValue((short)17, Xsub6.sys_correlation_nbr);//xsub0_relnbr所在的位置
					//rs.GetFieldValue((short)21, strVal_1);//日期xsub0_date
					rs.MoveNext();
				}
			}
			m_odbc.Close();
		}
	}
	catch (CDBException* e) {
		read_QRcode_flag = RESULT_MES_NETWORK_NG;
	}
	Xsub6.QRcode = strVal;
	//Xsub6.cur_work_order = strVal_1;
	return read_QRcode_flag;
}
void test()//关联操作
{
		//标签获取到，需要同底板进行关联
		//xsub0_domain  xsub0_nbr    xsub0_source      xsub0__chr01    xsub0_relnbr            xsub0_date         xsub0__dte01 =''
		//域名（0）      标签号（1）   关联码（2）        工单（3）    需要关联的标签（17）   关联时的日期（21）  更新时需要清空
		//步骤说明
		//1、查询底板标签是否有关联标签
		//2、查询标签是否与当前的测试的标签一致
		//3、存在关联关联关系则不更新，否则进行关系更新
		//4、关联关系确认后录入测试结果数据到数据库

		if(Xsub6.top_nbr !=  Xsub6.sys_correlation_nbr)//关联标签与测试标签不一致，则需要进行更新
		{
			CTime time = CTime::GetCurrentTime();
			CString m_strTime = time.Format("%Y-%m-%d");
			CString m_strTime1 = "2001-01-01";
			if(atoi(Xsub6.nbr) >= 60000000)//根据底板的标签来缩小搜索范围
			{
				str_tmp.Format(_T("update pub.xsub0_det set xsub0_relnbr = '%s',xsub0_date ='%s',xsub0__dte01 ='%s' where xsub0_domain = '001' and xsub0_nbr = '%s'"),Xsub6.top_nbr,m_strTime,m_strTime1,Xsub6.nbr);

			}
			else
			{
				str_tmp.Format(_T("update pub.xsub0_det set xsub0_relnbr = '%s',xsub0_date ='%s',xsub0__dte01 ='%s' where xsub0_domain = '003' and xsub0_nbr = '%s'"),Xsub6.top_nbr,m_strTime,m_strTime1,Xsub6.nbr);

			}
			try
			{
				m_odbc.Open(NULL,false,false,_T(DATABASE_OPEN_SQL),true);
				if(m_odbc.IsOpen())
				{
					try{
						m_odbc.ExecuteSQL(str_tmp);
						m_odbc.Close();
						//logstr.Format("(Xsub6.top_nbr = %s   Xsub6.sys_correlation_nbr = %s",Xsub6.top_nbr,Xsub6.sys_correlation_nbr);
						//AfxMessageBox("关联关系更新");
					}
					catch(CDBException *e){    
						AfxMessageBox(e->m_strError);
						AfxMessageBox(str_tmp);
						GetDlgItem(IDC_STATIC13)->SetWindowText("顶板底板标签关联失败");	
						Xsub6.operating_step = 0;
						PitchData_st.TestResult =  "顶板底板标签关联失败";
						GetDlgItem( IDC_RESULT_PITCH)->SetWindowText("不合格");
						PitchData_st.ThreadRecordeStartFlag = true;
						pWinThread->ResumeThread();//恢复线程
						GetDlgItem(IDC_BUTTON002)->EnableWindow(TRUE);
						Xsub6.PitchAngle_Updata =false;
						m_odbc.Close();
						return;
					}

				}
			}
			catch (CDBException* e) {
				GetDlgItem(IDC_STATIC13)->SetWindowText("打开数据库异常");	
				GetDlgItem(IDC_BUTTON002)->EnableWindow(TRUE);
				Xsub6.PitchAngle_Updata =false;
				return;
			}
		}
		else{
			CString logstr;
			//logstr.Format("(Xsub6.top_nbr = %s   Xsub6.sys_correlation_nbr = %s",Xsub6.top_nbr,Xsub6.sys_correlation_nbr);
			//AfxMessageBox(logstr);
		}


	}
	
	
	
}
//数据库操作
result_t CCoordinateDisplayDlg::WriteSqlMesSystem()
{

	if(atoi(Xsub6.nbr) >= 60000000) {
		Xsub6.domain = "001";
	} else {
		Xsub6.domain = "003";
	}

	result_t sql_result = RESULT_MES_NG;
	CTime time = CTime::GetCurrentTime();
	CString m_strTime = time.Format("%Y-%m-%d");
	CString search_str;
	bool search_success_flag =false;
	try
	{
		m_odbc.Open(NULL, false, false, _T(DATABASE_OPEN_SQL), true);
		if (m_odbc.IsOpen())
		{
			CString s;
			SYSTEMTIME st;
			GetLocalTime(&st);
			Xsub6.dateYear = st.wYear;
			Xsub6.dateMonth = st.wMonth;
			Xsub6.dateDay = st.wDay;
			Xsub6.time = st.wHour * 3600 + st.wMinute * 60 + st.wSecond;
			rs.m_pDatabase = &m_odbc;
			if(atoi(Xsub6.nbr) >= 60000000)
			{
				search_str.Format(_T("select MAX(xsub6_trnbr) from pub.xsub6_det where xsub6_date = '%s' and (xsub6_domain = '001') with(nolock)"),m_strTime);
			}
			else
			{
				search_str.Format(_T("select MAX(xsub6_trnbr) from pub.xsub6_det where xsub6_date = '%s' and (xsub6_domain = '003') with(nolock)"),m_strTime);
			}
			//先采用日期搜索，若日期未搜到，代表几台未开始测试，换成原最大搜索方式
			rs.Open(CRecordset::forwardOnly,search_str);//查找最大值 速度快
			if(rs.IsOpen())//
			{
				long temp=0;
				CString strVal;	
				while(!rs.IsEOF())
				{
					rs.GetFieldValue((short)0,strVal);
					if(strVal != "")
					{
						search_success_flag = true;
						Xsub6.trnbr = atol(strVal);
					}
					rs.MoveNext();
				}
				rs.Close();

			}
			//判断按日期搜索是否有找到，若没找到则采用最原始方式
			if(search_success_flag ==  false)
			{
				rs.Open(CRecordset::forwardOnly, _T("select MAX(xsub6_trnbr) from pub.xsub6_det with(nolock)"));//查找最大值  速度较慢
				if(rs.IsOpen())//
				{
					long temp=0;
					CString strVal;	
					while(!rs.IsEOF())
					{
						rs.GetFieldValue((short)0,strVal);
						Xsub6.trnbr = atol(strVal);
						rs.MoveNext();
					}
					rs.Close();

				}
			}
			++Xsub6.trnbr;//每一笔自加1 .需要从数据库中获取最大事务号
			s.Format(_T("insert into pub.xsub6_det (xsub6_domain, xsub6_trnbr, xsub6_nbr, xsub6_op, xsub6_userid, xsub6_date, xsub6_time, xsub6_rslt, xsub6_rsn_code,\
						xsub6__chr03,xsub6__chr04,xsub6__chr05,xsub6_newid,xsub6_uid,\
						xsub6_project1,xsub6_stand1,xsub6_act1, xsub6_hege1,\
						xsub6_project2,xsub6_stand2,xsub6_act2, xsub6_hege2)\
						values('%s', %d, '%s','%s', 'U%s', '%d/%d/%d', '%d', '%d', '%s',\
						'%s','%s','%s','%s','%s',\
						'%s','%s','%s','%d',\
						'%s','%s','%s','%d')"),
						Xsub6.domain, Xsub6.trnbr, Xsub6.nbr, Xsub6.op, Xsub6.userid, Xsub6.dateYear,Xsub6.dateMonth,Xsub6.dateDay, Xsub6.time, Xsub6.rslt, Xsub6.rsn_code,
						Xsub6.station,Xsub6.top_nbr,Xsub6.user_type,Xsub6.work_order,Xsub6.TopMcuIdStr,
						Xsub6.project[0],Xsub6.stand[0],Xsub6.act[0],Xsub6.hege[0],
						Xsub6.project[1],Xsub6.stand[1],Xsub6.act[1],Xsub6.hege[1]
			);
			try
			{
				m_odbc.ExecuteSQL(s);
				m_odbc.Close();
				sql_result = RESULT_MES_OK;
				return sql_result;
			}
			catch (CDBException* e)
			{
				m_odbc.Close();
				int index = e->m_strError.Find(_T(RESULT_MES_TrNbr_NGCode));
				if(-1 != index) {
					sql_result = RESULT_MES_TrNbr_NG;
				} else {
					sql_result = RESULT_MES_NG;
				}
				
				return sql_result;
			}

		}
		else
		{
			sql_result = RESULT_MES_NETWORK_NG;
			return sql_result;
		}
	}
	catch (CDBException* e)
	{
		sql_result = RESULT_MES_NETWORK_NG;
		return sql_result;
	}
	return sql_result;
}