# 俯仰角测试软件用户手册

## 1. 系统概述

俯仰角测试系统是一套完整的激光雷达俯仰角视觉检测解决方案，主要功能包括：

- **俯仰角视觉检测**：自动检测激光雷达的俯仰角参数
- **测试数据管理**：CSV格式的测试数据存储和管理
- **MES数据上传**：将测试结果批量上传到MES（制造执行系统）数据库
- **质量追溯**：完整的测试数据追溯和质量管控

### 1.1 核心优势

- **智能去重**：自动识别重复测试，只保留最新数据
- **断点续传**：支持异常中断后继续上传
- **数据保护**：原始文件自动备份，工作拷贝机制
- **状态跟踪**：实时显示上传进度和结果统计

## 2. 系统要求

### 2.1 硬件要求
- CPU: Intel i3或同等性能处理器
- 内存: 4GB RAM或以上
- 硬盘: 至少100MB可用空间
- 网络: 稳定的局域网连接

### 2.2 软件要求
- 操作系统: Windows 7/10/11
- .NET Framework 4.5或以上
- ODBC数据库驱动程序

## 3. 配置说明

### 3.1 MES配置文件 (MesInfo.xml)

配置文件位置：程序根目录下的`MesInfo.xml`

```xml
<?xml version="1.0" encoding="UTF-8"?>
<MesInfo>
    <!-- 生产信息配置 -->
    <workOrder>WO202501001</workOrder>          <!-- 工单号 -->
    <userID>operator01</userID>                 <!-- 操作员ID -->
    <preop>OP010</preop>                        <!-- 上道工序号 -->
    <op>OP020</op>                              <!-- 当前工序号 -->
    <station>ST001</station>                    <!-- 工站名称 -->
    
    <!-- 版本信息配置 -->
    <firmwareVersion>V1.2.3</firmwareVersion>   <!-- 固件版本 -->
    <snHardwareVersion>HW_V1.0</snHardwareVersion> <!-- 硬件版本 -->
    <snFirmwareVersion>SW_V2.1</snFirmwareVersion>  <!-- 软件版本 -->
    
    <!-- 数据库连接配置 -->
    <dataSourceName>MES_DB</dataSourceName>     <!-- 数据源名称 -->
    <hostName>*************</hostName>          <!-- 数据库服务器IP -->
    <port>1433</port>                           <!-- 数据库端口 -->
    <dataBaseUserID>mes_user</dataBaseUserID>   <!-- 数据库用户名 -->
    <password>mes_password</password>           <!-- 数据库密码 -->
</MesInfo>
```

### 3.2 配置项说明

| 配置项 | 必填 | 说明 | 示例值 |
|--------|------|------|--------|
| workOrder | ✓ | 当前生产工单号 | WO202501001 |
| userID | ✓ | 操作员工号 | operator01 |
| op | ✓ | 当前工序号 | OP020 |
| station | ✓ | 工站标识 | ST001 |
| firmwareVersion | ✓ | 期望的固件版本 | V1.2.3 |
| hostName | ✓ | MES数据库服务器地址 | ************* |
| port | ✓ | 数据库端口 | 1433 |
| dataBaseUserID | ✓ | 数据库用户名 | mes_user |
| password | ✓ | 数据库密码 | mes_password |

## 4. 使用说明

### 4.2 操作步骤

#### 4.2.1 启动程序
1. 双击运行`UploadPitchDataApp.exe`
2. 程序启动后会自动加载配置文件
3. 检查界面上的配置信息是否正确

#### 4.2.2 选择数据文件
1. 点击"选择文件"按钮
2. 浏览并选择要上传的CSV数据文件
3. 程序会自动检测文件格式和内容

#### 4.2.3 文件处理机制
程序采用智能文件处理机制：

**原始文件保护**：
- 如果选择的是原始测试文件，程序会自动创建工作拷贝
- 工作拷贝文件名格式：`原文件名_工作副本_时间戳.csv`
- 原始文件保持不变，所有操作在工作拷贝上进行

**录入状态跟踪**：
- 工作拷贝会增加"录入状态"列
- 状态值含义：
  - `1`：已成功录入MES
  - `空值`：未录入（待处理）
  - `-1`：录入异常（需重新处理）

#### 4.2.4 数据上传
1. 点击"开始上传"按钮
2. 程序会显示处理进度和状态信息
3. 上传过程中可以查看实时统计信息：
   - 总数据量
   - 已处理数量
   - 成功上传数量
   - 失败数量
   - 异常数量

#### 4.2.5 查看结果

上传完成后，所有状态信息都记录在工作拷贝文件中：

**状态跟踪方式**：
- 工作拷贝文件中的"录入状态"列实时更新
- `1`：成功上传到MES系统
- `空值`：未录入（待处理）
- `-1`：录入异常（需重新处理）

**结果统计**：
- 界面实时显示成功/失败/异常数量
- 日志文件记录详细的操作过程
- 支持断点续传，异常中断后可继续

## 5. 异常处理说明

### 5.1 常见异常及解决方法

| 异常类型 | 现象描述 | 可能原因 | 解决方法 | 优先级 |
|----------|----------|----------|----------|--------|
| **数据库连接异常** | 程序提示"无法打开数据库" | • 网络连接问题<br/>• 数据库服务器故障<br/>• 配置信息错误<br/>• 用户权限不足 | 1. 检查网络连接状态<br/>2. 验证数据库服务器运行状态<br/>3. 确认MesInfo.xml配置正确<br/>4. 联系管理员检查权限 | 🔴 高 |
| **固件版本不匹配** | 录入状态显示为-1<br/>日志记录版本异常 | • CSV文件版本与配置不一致<br/>• 测试设备固件版本错误 | 1. 检查测试设备固件版本<br/>2. 更新MesInfo.xml配置<br/>3. 升级设备固件版本 | 🟡 中 |
| **数据格式错误** | 提示"CSV data error"<br/>或"CSV data col error" | • CSV文件列数不足<br/>• 数据类型转换失败<br/>• 文件编码问题 | 1. 验证CSV文件格式完整性<br/>2. 检查数值列数据有效性<br/>3. 确保UTF-8或GBK编码 | 🟡 中 |
| **重复数据处理** | 同一电机标签多次测试 | • 重复测试操作<br/>• 历史数据未清理 | 1. 系统自动去重保留最新<br/>2. 检查工作拷贝状态列<br/>3. 手动清理历史数据 | 🟢 低 |
| **软件异常退出** | 程序意外关闭<br/>上传过程中断 | • 系统资源不足<br/>• 网络连接中断<br/>• 程序异常 | 1. 重启程序选择工作拷贝<br/>2. 检查系统资源状态<br/>3. 查看日志文件排查 | 🟡 中 |

### 5.2 断点续传功能

#### 5.2.1 软件异常退出恢复
如果程序在上传过程中异常退出：

1. **重新启动程序**
2. **选择之前的工作拷贝文件**（带有录入状态列的文件）
3. **程序会自动检测录入状态**：
   - 跳过已成功录入的数据（状态=1）
   - 重新处理未录入的数据（状态=空）
   - 重新处理异常数据（状态=-1）

#### 5.2.2 重复数据处理
程序采用智能去重机制：
- **逆序处理**：从文件底部开始处理，优先处理最新数据
- **标签去重**：同一电机标签只处理最新的一条记录
- **状态跳过**：已录入的数据自动跳过

### 5.3 错误代码说明

| 错误代码 | 含义 | 处理建议 |
|----------|------|----------|
| DB_CONN_FAIL | 数据库连接失败 | 检查网络和数据库配置 |
| VERSION_MISMATCH | 固件版本不匹配 | 更新配置或固件版本 |
| DATA_FORMAT_ERROR | 数据格式错误 | 检查CSV文件格式 |
| UPLOAD_RETRY_EXCEEDED | 上传重试次数超限 | 检查网络稳定性 |
| MES_INFO_INCOMPLETE | MES信息不完整 | 检查配置文件完整性 |

## 6. 维护与故障排除

### 6.1 日志文件
程序运行时会生成日志文件，位于程序目录的`logs`文件夹中：
- `upload_YYYYMMDD.log`：每日上传操作日志
- `error_YYYYMMDD.log`：错误信息日志

### 6.2 性能优化建议
1. **网络环境**：确保稳定的网络连接，避免网络波动
2. **数据量控制**：单次上传建议不超过1000条记录
3. **定期清理**：定期清理历史日志和结果文件
4. **数据库维护**：定期进行数据库性能优化

### 6.3 技术支持
如遇到无法解决的问题，请联系技术支持：
- 邮箱：<EMAIL>
- 电话：400-xxx-xxxx
- 提供信息：错误截图、日志文件、配置文件

---

**版本信息**：V1.0  
**更新日期**：2025-01-17  
**适用软件版本**：UploadPitchDataApp V1.0及以上
