{"cmake": {"generator": {"multiConfig": false, "name": "MinGW Makefiles"}, "paths": {"cmake": "D:/Programs/CMake/bin/cmake.exe", "cpack": "D:/Programs/CMake/bin/cpack.exe", "ctest": "D:/Programs/CMake/bin/ctest.exe", "root": "D:/Programs/CMake/share/cmake-3.21"}, "version": {"isDirty": false, "major": 3, "minor": 21, "patch": 2, "string": "3.21.2", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-411c875dfec06a48ba2a.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-7f510ecd53925ee97f8b.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-bb87d7750ebd1264d200.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, {"jsonFile": "toolchains-v1-18fb3e4af5cd6aaba78c.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"client-vscode": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}, {"kind": "cmakeFiles", "version": 1}], "responses": [{"jsonFile": "cache-v2-7f510ecd53925ee97f8b.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "codemodel-v2-411c875dfec06a48ba2a.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "toolchains-v1-18fb3e4af5cd6aaba78c.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-bb87d7750ebd1264d200.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}]}}}}