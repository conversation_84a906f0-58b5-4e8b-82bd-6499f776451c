# MES数据上传功能测试规划文档

## 1. 测试目标

### 1.1 主要目标
- 验证MES数据上传功能的正确性和稳定性
- 测试异常情况下的处理机制
- 验证代码优化后的功能完整性
- 确保数据安全和完整性

### 1.2 测试范围
- **功能测试**: 核心上传功能、去重逻辑、状态跟踪
- **异常测试**: 网络中断、数据库异常、文件损坏等
- **性能测试**: 大数据量处理、内存使用、响应时间
- **集成测试**: 与实际MES系统的集成验证

## 2. 测试原则

### 2.1 测试设计原则
1. **黑盒测试为主**: 从用户角度验证功能
2. **白盒测试为辅**: 验证内部逻辑和代码路径
3. **异常优先**: 重点测试各种异常情况
4. **真实环境**: 使用真实数据和实际程序代码
5. **自动化执行**: 可重复执行的自动化测试

### 2.2 测试数据原则
- 使用真实的CSV测试数据
- 构造各种异常数据场景
- 包含边界值和极限情况
- 模拟生产环境的数据特征

## 3. 测试架构

### 3.1 测试层次结构

```mermaid
graph TD
    A[单元测试] --> B[集成测试]
    B --> C[系统测试]
    C --> D[验收测试]
    
    A1[CSV处理单元] --> A
    A2[数据库操作单元] --> A
    A3[状态管理单元] --> A
    
    B1[文件处理集成] --> B
    B2[MES上传集成] --> B
    B3[异常处理集成] --> B
    
    C1[完整流程测试] --> C
    C2[性能压力测试] --> C
    C3[异常恢复测试] --> C
    
    D1[用户场景验证] --> D
    D2[生产环境测试] --> D
```

### 3.2 测试工具链
- **测试框架**: Qt Test Framework
- **Mock工具**: 模拟数据库连接和网络异常
- **数据生成**: 自动生成测试CSV文件
- **结果验证**: 自动比对预期结果

## 4. 测试用例设计

### 4.1 功能测试用例

| 用例ID | 测试场景 | 输入数据 | 预期结果 | 优先级 |
|--------|----------|----------|----------|--------|
| TC001 | 正常CSV文件上传 | 标准格式CSV文件 | 全部数据成功上传 | P0 |
| TC002 | 重复数据去重 | 包含重复电机标签的CSV | 只保留最新数据 | P0 |
| TC003 | 工作拷贝创建 | 原始CSV文件 | 自动创建带状态列的拷贝 | P0 |
| TC004 | 状态跟踪更新 | 部分已录入的工作拷贝 | 只处理未录入数据 | P0 |
| TC005 | 断点续传 | 中断后的工作拷贝 | 继续未完成的上传 | P1 |

### 4.2 异常测试用例

| 用例ID | 异常场景 | 触发条件 | 预期处理 | 优先级 |
|--------|----------|----------|----------|--------|
| TC101 | 数据库连接失败 | 错误的连接配置 | 显示错误信息，保存状态 | P0 |
| TC102 | 网络中断 | 上传过程中断网 | 重试机制，状态回滚 | P0 |
| TC103 | CSV格式错误 | 缺少列或格式错误 | 错误提示，跳过错误行 | P1 |
| TC104 | 固件版本不匹配 | 版本号不一致 | 记录异常状态-1 | P1 |
| TC105 | 文件权限错误 | 只读文件或无权限 | 权限错误提示 | P2 |
| TC106 | 内存不足 | 大文件处理 | 分批处理或错误提示 | P2 |
| TC107 | 程序异常退出 | 强制关闭程序 | 状态保存，支持恢复 | P1 |

### 4.3 性能测试用例

| 用例ID | 性能场景 | 测试条件 | 性能指标 | 优先级 |
|--------|----------|----------|----------|--------|
| TC201 | 大数据量处理 | 1000条记录 | <30秒完成 | P1 |
| TC202 | 内存使用 | 连续处理多个文件 | <100MB内存 | P2 |
| TC203 | 并发处理 | 多个文件同时处理 | 无数据冲突 | P2 |

## 5. 测试实现

### 5.1 测试程序结构

```cpp
class MESFunctionTest : public QObject {
    Q_OBJECT
    
private slots:
    // 功能测试
    void testNormalUpload();
    void testDuplicateRemoval();
    void testWorkingCopyCreation();
    void testStatusTracking();
    void testResumeUpload();
    
    // 异常测试
    void testDatabaseConnectionFailure();
    void testNetworkInterruption();
    void testInvalidCSVFormat();
    void testFirmwareVersionMismatch();
    void testFilePermissionError();
    void testMemoryLimitExceeded();
    void testProgramCrash();
    
    // 性能测试
    void testLargeDataProcessing();
    void testMemoryUsage();
    void testConcurrentProcessing();
    
private:
    Widget* widget;
    CSVReader* csvReader;
    QString testDataDir;
    QString backupDir;
};
```

### 5.2 测试数据准备

```cpp
class TestDataGenerator {
public:
    // 生成标准测试数据
    QString generateNormalCSV(int recordCount);
    
    // 生成包含重复数据的CSV
    QString generateDuplicateCSV();
    
    // 生成格式错误的CSV
    QString generateInvalidCSV();
    
    // 生成大数据量CSV
    QString generateLargeCSV(int recordCount);
    
    // 生成特定异常场景的CSV
    QString generateExceptionCSV(ExceptionType type);
};
```

### 5.3 Mock对象设计

```cpp
class MockDatabase : public QObject {
    Q_OBJECT
public:
    void setConnectionFailure(bool fail);
    void setNetworkDelay(int milliseconds);
    void setRandomFailureRate(double rate);
    
signals:
    void connectionFailed();
    void uploadSuccess(const QString& nbr);
    void uploadFailed(const QString& nbr, const QString& error);
};
```

## 6. 测试执行计划

### 6.1 测试阶段

| 阶段 | 测试内容 | 执行时间 | 负责人 |
|------|----------|----------|--------|
| 阶段1 | 单元测试 | 1天 | 开发人员 |
| 阶段2 | 集成测试 | 2天 | 测试人员 |
| 阶段3 | 系统测试 | 3天 | 测试团队 |
| 阶段4 | 验收测试 | 2天 | 用户代表 |

### 6.2 测试环境要求

**开发测试环境**:
- Windows 10/11
- Qt 5.15+
- 模拟MES数据库
- 测试数据集

**集成测试环境**:
- 真实MES数据库连接
- 网络环境模拟工具
- 性能监控工具

**生产验证环境**:
- 实际生产环境
- 真实测试数据
- 完整的监控和日志

## 7. 测试验收标准

### 7.1 功能验收标准
- ✅ 所有P0用例100%通过
- ✅ 所有P1用例95%以上通过
- ✅ 所有P2用例90%以上通过
- ✅ 无严重缺陷(Blocker/Critical)

### 7.2 性能验收标准
- ✅ 1000条记录处理时间<30秒
- ✅ 内存使用<100MB
- ✅ 99%的操作响应时间<5秒

### 7.3 稳定性验收标准
- ✅ 连续运行24小时无崩溃
- ✅ 异常恢复成功率>95%
- ✅ 数据完整性100%保证

## 8. 风险评估

### 8.1 技术风险
- **数据库兼容性**: 不同版本MES数据库的兼容问题
- **网络稳定性**: 生产环境网络不稳定导致的上传失败
- **数据安全**: 测试过程中的数据泄露风险

### 8.2 进度风险
- **环境准备**: 测试环境搭建时间可能超预期
- **数据准备**: 真实测试数据获取困难
- **人员配置**: 测试人员技能和时间安排

### 8.3 风险缓解措施
- 提前准备多套测试环境
- 建立完整的测试数据库
- 制定详细的测试执行手册
- 建立问题快速响应机制

---

**文档版本**: V1.0  
**创建日期**: 2025-01-17  
**更新日期**: 2025-01-17  
**审核状态**: 待审核
