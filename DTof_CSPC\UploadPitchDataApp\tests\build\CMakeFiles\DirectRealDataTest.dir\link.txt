D:\Programs\CMake\bin\cmake.exe -E rm -f CMakeFiles\DirectRealDataTest.dir/objects.a
D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\ar.exe qc CMakeFiles\DirectRealDataTest.dir/objects.a @CMakeFiles\DirectRealDataTest.dir\objects1.rsp
D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\g++.exe  -finput-charset=UTF-8 -O3 -DNDEBUG -Wl,--whole-archive CMakeFiles\DirectRealDataTest.dir/objects.a -Wl,--no-whole-archive -o ..\bin\DirectRealDataTest.exe -Wl,--out-implib,libDirectRealDataTest.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\DirectRealDataTest.dir\linklibs.rsp
