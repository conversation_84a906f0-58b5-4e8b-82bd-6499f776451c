/********************************************************************************
** Form generated from reading UI file 'Comm.ui'
**
** Created by: Qt User Interface Compiler version 5.14.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_COMM_H
#define UI_COMM_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_Comm
{
public:
    QVBoxLayout *verticalLayout;
    QHBoxLayout *horizontalLayout;
    QLabel *virtualUartLabel;
    QComboBox *virtualUartCombox;
    QPushButton *virtualUartBtn;
    QHBoxLayout *horizontalLayout_2;
    QLabel *lidarUartLabel;
    QComboBox *lidarUartCombox;
    QPushButton *lidarUartBtn;

    void setupUi(QWidget *Comm)
    {
        if (Comm->objectName().isEmpty())
            Comm->setObjectName(QString::fromUtf8("Comm"));
        Comm->resize(518, 123);
        verticalLayout = new QVBoxLayout(Comm);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        horizontalLayout = new QHBoxLayout();
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        virtualUartLabel = new QLabel(Comm);
        virtualUartLabel->setObjectName(QString::fromUtf8("virtualUartLabel"));
        virtualUartLabel->setMinimumSize(QSize(0, 0));
        virtualUartLabel->setMaximumSize(QSize(100, 16777215));

        horizontalLayout->addWidget(virtualUartLabel);

        virtualUartCombox = new QComboBox(Comm);
        virtualUartCombox->setObjectName(QString::fromUtf8("virtualUartCombox"));

        horizontalLayout->addWidget(virtualUartCombox);

        virtualUartBtn = new QPushButton(Comm);
        virtualUartBtn->setObjectName(QString::fromUtf8("virtualUartBtn"));
        virtualUartBtn->setMaximumSize(QSize(80, 16777215));

        horizontalLayout->addWidget(virtualUartBtn);


        verticalLayout->addLayout(horizontalLayout);

        horizontalLayout_2 = new QHBoxLayout();
        horizontalLayout_2->setObjectName(QString::fromUtf8("horizontalLayout_2"));
        lidarUartLabel = new QLabel(Comm);
        lidarUartLabel->setObjectName(QString::fromUtf8("lidarUartLabel"));
        lidarUartLabel->setMaximumSize(QSize(100, 16777215));

        horizontalLayout_2->addWidget(lidarUartLabel);

        lidarUartCombox = new QComboBox(Comm);
        lidarUartCombox->setObjectName(QString::fromUtf8("lidarUartCombox"));

        horizontalLayout_2->addWidget(lidarUartCombox);

        lidarUartBtn = new QPushButton(Comm);
        lidarUartBtn->setObjectName(QString::fromUtf8("lidarUartBtn"));
        lidarUartBtn->setMaximumSize(QSize(80, 16777215));

        horizontalLayout_2->addWidget(lidarUartBtn);


        verticalLayout->addLayout(horizontalLayout_2);


        retranslateUi(Comm);

        QMetaObject::connectSlotsByName(Comm);
    } // setupUi

    void retranslateUi(QWidget *Comm)
    {
        Comm->setWindowTitle(QCoreApplication::translate("Comm", "Form", nullptr));
        virtualUartLabel->setText(QCoreApplication::translate("Comm", "\350\275\257\344\273\266\351\200\232\350\256\257\344\270\262\345\217\243:", nullptr));
        virtualUartBtn->setText(QCoreApplication::translate("Comm", "\346\211\223\345\274\200", nullptr));
        lidarUartLabel->setText(QCoreApplication::translate("Comm", "\351\233\267\350\276\276\351\200\232\350\256\257\344\270\262\345\217\243:", nullptr));
        lidarUartBtn->setText(QCoreApplication::translate("Comm", "\346\211\223\345\274\200", nullptr));
    } // retranslateUi

};

namespace Ui {
    class Comm: public Ui_Comm {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_COMM_H
