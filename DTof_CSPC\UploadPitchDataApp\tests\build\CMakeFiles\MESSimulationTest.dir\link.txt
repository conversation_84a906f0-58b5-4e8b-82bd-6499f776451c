D:\Programs\CMake\bin\cmake.exe -E rm -f CMakeFiles\MESSimulationTest.dir/objects.a
D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\ar.exe qc CMakeFiles\MESSimulationTest.dir/objects.a @CMakeFiles\MESSimulationTest.dir\objects1.rsp
D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\g++.exe  -finput-charset=UTF-8 -O3 -DNDEBUG -Wl,--whole-archive CMakeFiles\MESSimulationTest.dir/objects.a -Wl,--no-whole-archive -o ..\bin\MESSimulationTest.exe -Wl,--out-implib,libMESSimulationTest.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\MESSimulationTest.dir\linklibs.rsp
