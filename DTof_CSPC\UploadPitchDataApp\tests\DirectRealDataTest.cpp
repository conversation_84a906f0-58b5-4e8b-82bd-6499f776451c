#include <QApplication>
#include <QDebug>
#include <QtTest/QtTest>
#include <QFileInfo>
#include <QDir>
#include <QSignalSpy>
#include "../widget.h"
#include "../CSV/CSVReader.h"

/**
 * @brief 直接使用真实CSV文件的测试类
 * 
 * 测试修改后的CSVReader能否直接处理真实的CSV文件格式
 * 无需格式转换，直接使用原始数据
 */
class DirectRealDataTest : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // 核心测试用例
    void testDirectRealCSVReading();
    void testRealDataMESUpload();
    void testRealDataWorkingCopy();
    void testRealDataDuplicateRemoval();

private:
    Widget* m_widget;
    CSVReader* m_csvReader;
    QString m_realDataFile;
    QString m_testDataDir;
};

void DirectRealDataTest::initTestCase()
{
    qDebug() << "=== 直接真实CSV文件测试开始 ===";
    
    m_testDataDir = QCoreApplication::applicationDirPath() + "/test_data/";
    QDir().mkpath(m_testDataDir);
    
    m_realDataFile = m_testDataDir + "real_data.csv";
    
    qDebug() << "测试数据目录:" << m_testDataDir;
    qDebug() << "真实数据文件:" << m_realDataFile;
}

void DirectRealDataTest::cleanupTestCase()
{
    qDebug() << "=== 直接真实CSV文件测试结束 ===";
}

void DirectRealDataTest::init()
{
    m_widget = new Widget();
    m_csvReader = new CSVReader();
}

void DirectRealDataTest::cleanup()
{
    delete m_widget;
    delete m_csvReader;
    m_widget = nullptr;
    m_csvReader = nullptr;
}

void DirectRealDataTest::testDirectRealCSVReading()
{
    qDebug() << "\n--- 测试直接读取真实CSV文件 ---";
    
    if (!QFile::exists(m_realDataFile)) {
        QSKIP("真实数据文件不存在");
        return;
    }
    
    // 直接使用修改后的CSVReader读取真实CSV文件
    QVector<PitchData> pitchData = m_csvReader->readDataFromCsv(m_realDataFile);
    
    qDebug() << "直接读取真实CSV结果:" << pitchData.size() << "条数据";
    
    QVERIFY(pitchData.size() > 0);
    
    if (pitchData.size() > 0) {
        const PitchData& firstData = pitchData[0];
        qDebug() << "第一条真实数据:";
        qDebug() << "- 电机标签:" << firstData.getNbr();
        qDebug() << "- MCU ID:" << firstData.getMcuID();
        qDebug() << "- 固件版本:" << firstData.getFirmwareVersion();
        qDebug() << "- 测试结果:" << firstData.getTestResult();
        qDebug() << "- 最小俯仰角:" << firstData.getPitchAngleValueMin();
        qDebug() << "- 最大俯仰角:" << firstData.getPitchAngleValueMax();
        
        QVERIFY(!firstData.getNbr().isEmpty());
        QVERIFY(!firstData.getMcuID().isEmpty());
        QCOMPARE(firstData.getFirmwareVersion(), QString("V1.6.15.11.25.2.25"));
        QCOMPARE(firstData.getTestResult(), 1);
    }
    
    // 验证所有数据
    for (const PitchData& data : pitchData) {
        QVERIFY(!data.getNbr().isEmpty());
        QVERIFY(!data.getMcuID().isEmpty());
        QVERIFY(!data.getFirmwareVersion().isEmpty());
        QVERIFY(data.getTestResult() == 0 || data.getTestResult() == 1);
    }
    
    qDebug() << "✅ 直接读取真实CSV文件测试通过";
}

void DirectRealDataTest::testRealDataMESUpload()
{
    qDebug() << "\n--- 测试真实数据MES上传流程 ---";
    
    if (!QFile::exists(m_realDataFile)) {
        QSKIP("真实数据文件不存在");
        return;
    }
    
    // 读取真实数据
    QVector<PitchData> pitchData = m_csvReader->readDataFromCsv(m_realDataFile);
    qDebug() << "读取到真实数据:" << pitchData.size() << "条";
    QVERIFY(pitchData.size() > 0);
    
    // 测试去重功能
    QVector<PitchData> uniqueData = m_csvReader->removeDuplicatesKeepLatest(pitchData);
    qDebug() << "去重前:" << pitchData.size() << "条，去重后:" << uniqueData.size() << "条";
    QVERIFY(uniqueData.size() <= pitchData.size());
    
    // 测试过滤需要处理的数据
    QVector<PitchData> needsProcessing = m_csvReader->filterNeedsProcessing(uniqueData);
    qDebug() << "需要处理的数据:" << needsProcessing.size() << "条";
    
    // 测试获取实际上传数据
    QVector<PitchData> actualUploadData = m_csvReader->getActualUploadData(pitchData);
    qDebug() << "实际需要上传的数据:" << actualUploadData.size() << "条";
    
    qDebug() << "✅ 真实数据MES上传流程测试通过";
}

void DirectRealDataTest::testRealDataWorkingCopy()
{
    qDebug() << "\n--- 测试真实数据工作拷贝功能 ---";
    
    if (!QFile::exists(m_realDataFile)) {
        QSKIP("真实数据文件不存在");
        return;
    }
    
    // 检查是否为工作拷贝
    bool isWorkingCopy = m_csvReader->isWorkingCopy(m_realDataFile);
    qDebug() << "原始文件是否为工作拷贝:" << isWorkingCopy;
    
    // 创建工作拷贝
    QString workingCopyPath = m_csvReader->createWorkingCopy(m_realDataFile);
    QVERIFY(!workingCopyPath.isEmpty());
    QVERIFY(QFile::exists(workingCopyPath));
    qDebug() << "工作拷贝创建成功:" << workingCopyPath;
    
    // 验证工作拷贝是否被正确识别
    bool isWorkingCopyCheck = m_csvReader->isWorkingCopy(workingCopyPath);
    QVERIFY(isWorkingCopyCheck);
    qDebug() << "工作拷贝验证通过";
    
    // 读取工作拷贝数据
    QVector<PitchData> workingCopyData = m_csvReader->readDataFromCsv(workingCopyPath);
    qDebug() << "工作拷贝数据条数:" << workingCopyData.size();
    QVERIFY(workingCopyData.size() > 0);
    
    qDebug() << "✅ 真实数据工作拷贝功能测试通过";
}

void DirectRealDataTest::testRealDataDuplicateRemoval()
{
    qDebug() << "\n--- 测试真实数据去重功能 ---";
    
    if (!QFile::exists(m_realDataFile)) {
        QSKIP("真实数据文件不存在");
        return;
    }
    
    // 读取真实数据
    QVector<PitchData> allData = m_csvReader->readDataFromCsv(m_realDataFile);
    qDebug() << "原始数据条数:" << allData.size();
    QVERIFY(allData.size() > 0);
    
    // 统计电机标签
    QMap<QString, int> nbrCount;
    for (const PitchData& data : allData) {
        nbrCount[data.getNbr()]++;
    }
    
    qDebug() << "电机标签统计:";
    for (auto it = nbrCount.begin(); it != nbrCount.end(); ++it) {
        qDebug() << "- " << it.key() << ":" << it.value() << "条";
    }
    
    // 执行去重
    QVector<PitchData> uniqueData = m_csvReader->removeDuplicatesKeepLatest(allData);
    qDebug() << "去重后数据条数:" << uniqueData.size();
    
    // 验证去重结果
    QVERIFY(uniqueData.size() <= allData.size());
    QCOMPARE(uniqueData.size(), nbrCount.size());  // 去重后的数量应该等于唯一电机标签的数量
    
    // 验证每个电机标签只出现一次
    QSet<QString> uniqueNbrs;
    for (const PitchData& data : uniqueData) {
        QVERIFY(!uniqueNbrs.contains(data.getNbr()));
        uniqueNbrs.insert(data.getNbr());
    }
    
    qDebug() << "✅ 真实数据去重功能测试通过";
}

QTEST_MAIN(DirectRealDataTest)
#include "DirectRealDataTest.moc"
