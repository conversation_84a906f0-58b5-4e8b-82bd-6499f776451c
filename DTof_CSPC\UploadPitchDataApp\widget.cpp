#include "widget.h"
#include "./ui_widget.h"

#include <QCoreApplication>
#include <QDateTime>
#include <QDebug>
#include <QFile>
#include <QFileDialog>
#include <QMessageBox>


Widget::Widget(QWidget *parent) : QWidget(parent), ui(new Ui::Widget) {
    ui->setupUi(this);

    setWindowIcon(QIcon(":/resource/cspc.jpg"));
    // setWindowTitle("俯仰角数据上传V1.0");

    QString configFilePath = QCoreApplication::applicationDirPath() + "/MesInfo.xml";
    qDebug() << configFilePath;
    QFile file(configFilePath);
    if (!file.exists()) {
        //不存在配置文件 创建
        qDebug() << "Start creating the config file.";
        if (configLoader.createXmlConfig(config, configFilePath)) {
            qDebug() << "Config file created successfully.";
        } else {
            qDebug() << "Config file creation failed";
        }
    } else {
        //存在配置文件 读取
        qDebug() << "Start reading the Config file.";
        if (configLoader.readXmlconfig(config, file)) {
            qDebug() << "Config file read successfully.";
        } else {
            qDebug() << "Config file reading failed.";
        }
    }

    //不指定连接名 使用默认的数据库连接
    odbcDB = QSqlDatabase::addDatabase("QODBC");
    odbcDB.setHostName(config.getHostName());
    odbcDB.setDatabaseName(config.getDataSourceName());
    odbcDB.setPort(config.getPort().toUInt());
    odbcDB.setUserName(config.getDataBaseUserID());
    odbcDB.setPassword(config.getPassword());


    connect(this, &Widget::uploadMesDataSignal, this, &Widget::uploadMesDataSlot);

    connect(this, &Widget::updateUploadMESSizeCnt, [&](int size) -> void { ui->uploadMESSizeCnt->setText(QString::number(size, 10)); });

    connect(this, &Widget::updateFirmwareVersionSizeCnt, [&](int size) -> void {
        ui->firmwareVersionErrSizeCnt->setText(QString::number(size, 10));
        if (size > 0) {
            ui->firmwareVersionErrSizeCnt->setStyleSheet("color: red");
        } else {
            ui->firmwareVersionErrSizeCnt->setStyleSheet("color: black");
        }
    });


    connect(this, &Widget::updateMesInfoErrSizeCnt, [&](int size) -> void {
        ui->mesInfoErrSizeCnt->setText(QString::number(size, 10));
        if (size > 0) {
            ui->mesInfoErrSizeCnt->setStyleSheet("color: red");
        } else {
            ui->mesInfoErrSizeCnt->setStyleSheet("color: black");
        }
    });
    connect(this, &Widget::updateMesUploadErrSizeCnt, [&](int size) -> void {
        ui->mesUploadErrSizeCnt->setText(QString::number(size, 10));
        if (size > 0) {
            ui->mesUploadErrSizeCnt->setStyleSheet("color: red");
        } else {
            ui->mesUploadErrSizeCnt->setStyleSheet("color: black");
        }
    });
    connect(this, &Widget::updateMesUploadSuccessSizeCnt, [&](int size) -> void { ui->mesUploadSuccessSizeCnt->setText(QString::number(size, 10)); });
}

Widget::~Widget() {
    delete ui;
}


void Widget::on_selectUploadFileBtn_clicked() {
    QString fileName = QFileDialog::getOpenFileName(this, tr("选择文件"), QCoreApplication::applicationDirPath(), tr("Csv (*.csv)"));
    qDebug() << fileName;
    if (fileName.isEmpty()) {
        QMessageBox::warning(this, "警告", "请选择要上传的文件");
        return;
    }

    const auto &sourceData = csvReader.readDataFromCsv(fileName);
    emit        updateUploadMESSizeCnt(sourceData.size());
    if (sourceData.isEmpty()) {
        QMessageBox::information(this, "提示", "请选择有效文件");
        return;
    }

    mesDataFileName = QDateTime::currentDateTime().toString("yyyyMMddhhmmss");

    //获取配置文件中需要的信息
    QString workOrderLocal               = config.getWorkOrder();
    QString userIDLocal                  = config.getUserID();
    QString preopLocal                   = config.getPreop();
    QString opLocal                      = config.getOp();
    QString stationLocal                 = config.getStation();
    QString standardFirmwareVersionLocal = config.getFirmwareVersion();
    QString snHardwareVersionLocal       = config.getSnHardwareVersion();
    QString snFirmwareVersionLocal       = config.getSnFirmwareVersion();

    if (SqlErrorType::NoError != dataStore.open(odbcDB)) {
        qDebug() << "Cannot open the database, aborting MES information detection.";
        QMessageBox::information(this, "提示", "无法打开数据库 中止MES信息检测 请检查网络重新上传数据");
        return;
    }

    QVector<PitchData> firmewareVersionErrorData;

    QVector<PitchData> mesInfoErrorData;

    QVector<MESData> mesData;
    for (const auto &item : sourceData) {

        QString nbrLocal                        = item.getNbr();  // 电机标签
        QString mcuIDLocal                      = item.getMcuID();
        QString firmwareVersionLocal            = item.getFirmwareVersion();
        int     testResultLocal                 = item.getTestResult();
        float   pitchAngleValueMinLocal         = item.getPitchAngleValueMin();
        float   pitchAngleValueMaxLocal         = item.getPitchAngleValueMax();
        float   pitchAngleValueStandardMinLocal = item.getPitchAngleValueStandardMin();
        float   pitchAngleValueStandardMaxLocal = item.getPitchAngleValueStandardMax();

        if (firmwareVersionLocal != standardFirmwareVersionLocal) {
            qDebug() << item.getNbr() << "software version error";
            firmewareVersionErrorData.push_back(item);
            continue;
        }

        QString domainLocal;
        if (nbrLocal.toUInt() >= 60000000) {  //根据电机标签数值，区分 domain
            domainLocal = "001";
        } else {
            domainLocal = "003";
        }
        uint8_t flag;

        // 1. 获取主板标签
        QString topNbrLocal;  //获取的主板标签
        flag = dataStore.getVirtualNbr(odbcDB, mcuIDLocal, workOrderLocal, topNbrLocal, domainLocal);
        if (SqlErrorType::NoError != flag) {
            mesInfoErrorData.push_back(item);
            continue;
        }

        // 2. 检测电机关联表MES信息

        //标签获取到，需要同底板进行关联
        // xsub0_domain  xsub0_nbr    xsub0_source      xsub0__chr01    xsub0_relnbr            xsub0_date         xsub0__dte01 =''
        //域名（0）      电机标签（1）   关联码（2）        工单（3）    需要关联的虚拟标签(主板标签)（17）   关联时的日期（21）  更新时需要清空
        //步骤说明
        // 2.1、查询底板标签是否有关联标签
        // 2.2、查询标签是否与当前的测试的标签一致
        // 2.3、存在关联关联关系则不更新，否则进行关系更新
        // 2.4、关联关系确认后录入测试结果数据到数据库
        QString topNbrTmp;    //与电机标签关联的主板标签
        QString snCodeLocal;  //查询到的电机SN码(明码，在二维码标签中可以看见)
        flag =
            dataStore.checkMesInfoForNbr(odbcDB, topNbrTmp, nbrLocal, domainLocal, workOrderLocal, snCodeLocal, snHardwareVersionLocal, snFirmwareVersionLocal);
        if (SqlErrorType::NoError != flag) {
            mesInfoErrorData.push_back(item);
            continue;
        }

        // 3. 检测主板标签MES信息
        // flag = dataStore.checkMesInfoForTopNbr(odbcDB, topNbrLocal, preopLocal, workOrderLocal);
        // if (SqlErrorType::NoError != flag) {
        //     mesInfoErrorData.push_back(item);
        //     continue;
        // }

        // 4. 生成除了事务号之外的所有数据
        MESData target;
        target.domain = domainLocal;
        target.nbr    = nbrLocal;
        target.op     = opLocal;
        target.userID = userIDLocal;
        QDateTime::currentDateTime().toString();
        target.date = QDate::currentDate().toString("yyyy/MM/dd");
        {
            uint8_t hours  = QTime::currentTime().toString("H").toUInt();
            uint8_t minute = QTime::currentTime().toString("m").toUInt();
            uint8_t second = QTime::currentTime().toString("s").toUInt();
            target.time    = hours * 3600 + minute * 60 + second;
        }
        target.testResult = testResultLocal;
        target.rsnCode    = target.testResult == 1 ? "PASS" : "NG";
        target.station    = stationLocal;
        target.topNbr     = topNbrLocal;
        target.workOrder  = workOrderLocal;
        target.mcuID      = mcuIDLocal;

        QString stand;
        stand         = QString("%1f~%2f").arg(pitchAngleValueStandardMinLocal).arg(pitchAngleValueStandardMaxLocal);
        target.stand1 = stand;
        target.act1   = pitchAngleValueMinLocal;
        target.hege1  = isHege(target.act1, pitchAngleValueStandardMinLocal, pitchAngleValueStandardMaxLocal);

        target.stand2 = stand;
        target.act2   = pitchAngleValueMaxLocal;
        target.hege2  = isHege(target.act2, pitchAngleValueStandardMinLocal, pitchAngleValueStandardMaxLocal);

        target.isNeedUpdateTopNbr = topNbrLocal == topNbrTmp ? false : true;
        target.SNCode             = snCodeLocal;
        mesData.push_back(target);
    }

    dataStore.close(odbcDB);
    //将mesInfoErrorData中的数据存储起来

    emit updateFirmwareVersionSizeCnt(firmewareVersionErrorData.size());
    csvWriter.writeMesInfoErrorData(firmewareVersionErrorData, mesDataFileName, CSVFileType::FirmewareVersionErr);

    emit updateMesInfoErrSizeCnt(mesInfoErrorData.size());
    csvWriter.writeMesInfoErrorData(mesInfoErrorData, mesDataFileName, CSVFileType::MESInfoErr);


    //接下来进行更新关联标签 上传数据的操作
    emit uploadMesDataSignal(mesData, mesDataFileName);
}

void Widget::uploadMesDataSlot(QVector<MESData> &mesData, const QString &time) {
    if (SqlErrorType::NoError != dataStore.open(odbcDB)) {
        qDebug() << "Cannot open the database, aborting association and data upload.";
        QMessageBox::information(this, "提示", "无法打开数据库 中止MES信息检测 请检查网络重新上传数据");
        return;
    }

    QVector<MESData> uploadMesErrorData;
    QVector<MESData> uploadMessuccessData;
    for (MESData &item : mesData) {
        // 1. 关联
        if (item.isNeedUpdateTopNbr) {
            // 重新关联电机码-虚拟标签
            if (SqlErrorType::NoError != dataStore.updataTopNbrForNbr(odbcDB, item.nbr, item.topNbr, item.domain)) {
                QMessageBox::information(this, "提示", QString("%1 %2关联失败 请重新上传数据").arg(item.nbr).arg(item.topNbr));
                return;
            }
        }

        const uint8_t tryCntMax = 5;
        uint8_t       tryCnt    = 0;
        for (; tryCnt < tryCntMax; tryCnt++) {
            // 2. 获取事务号
            uint32_t trnbrLocal = 0;
            if (SqlErrorType::NoError != dataStore.findXsubTrnbrMax(odbcDB, "xsub6", trnbrLocal, item.domain)) {
                continue;
            }

            item.trnbr = trnbrLocal;

            // 3. 上传数据
            if (SqlErrorType::NoError != dataStore.uploadMesData(odbcDB, item)) {
                continue;
            }

            //执行到此处 说明该项数据已成功上传
            uploadMessuccessData.push_back(item);
            break;
        }

        if (tryCnt >= tryCntMax) {
            //执行到此处 说明该项数据上传失败 需要把此项数据保存起来
            uploadMesErrorData.push_back(item);
        }
    }

    dataStore.close(odbcDB);

    //保存上传失败的MES数据
    emit updateMesUploadErrSizeCnt(uploadMesErrorData.size());
    csvWriter.writeMesData(uploadMesErrorData, time, CSVFileType::MESUploadFailed);

    //保存上传成功的MES数据
    emit updateMesUploadSuccessSizeCnt(uploadMessuccessData.size());
    csvWriter.writeMesData(uploadMessuccessData, time, CSVFileType::MesUploadSuccess);
}

bool Widget::isHege(float value, float min, float max) {
    if (value >= min && value <= max)
        return true;
    return false;
}
