D:\Programs\CMake\bin\cmake.exe -E rm -f CMakeFiles\SimpleTest.dir/objects.a
D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\ar.exe qc CMakeFiles\SimpleTest.dir/objects.a @CMakeFiles\SimpleTest.dir\objects1.rsp
D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\g++.exe  -finput-charset=UTF-8 -O3 -DNDEBUG -Wl,--whole-archive CMakeFiles\SimpleTest.dir/objects.a -Wl,--no-whole-archive -o ..\bin\SimpleTest.exe -Wl,--out-implib,libSimpleTest.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\SimpleTest.dir\linklibs.rsp
