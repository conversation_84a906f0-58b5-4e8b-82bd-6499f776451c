D:\Programs\CMake\bin\cmake.exe -E rm -f CMakeFiles\MESFunctionTest.dir/objects.a
D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\ar.exe qc CMakeFiles\MESFunctionTest.dir/objects.a @CMakeFiles\MESFunctionTest.dir\objects1.rsp
D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\g++.exe  -finput-charset=UTF-8 -O3 -DNDEBUG -Wl,--whole-archive CMakeFiles\MESFunctionTest.dir/objects.a -Wl,--no-whole-archive -o ..\bin\MESFunctionTest.exe -Wl,--out-implib,libMESFunctionTest.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\MESFunctionTest.dir\linklibs.rsp
