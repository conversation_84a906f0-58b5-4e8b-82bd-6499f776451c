# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.21

# compile CXX with D:/Programs/Qt/Qt5.14.2/Tools/mingw730_64/bin/g++.exe
CXX_DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MESSAGELOGCONTEXT -DQT_NO_DEBUG -DQT_SQL_LIB -DQT_TESTCASE_BUILDDIR=\"F:/13_Yapha-Laser-DTof2dMS/development/tool/yapha-proj-mes-02/DTof_CSPC/UploadPitchDataApp/tests/build\" -DQT_TESTLIB_LIB -DQT_WIDGETS_LIB -DQT_XML_LIB -DWIN32_LEAN_AND_MEAN

CXX_INCLUDES = @CMakeFiles/MESSimulationTest.dir/includes_CXX.rsp

CXX_FLAGS =  -finput-charset=UTF-8 -O3 -DNDEBUG -std=gnu++11

