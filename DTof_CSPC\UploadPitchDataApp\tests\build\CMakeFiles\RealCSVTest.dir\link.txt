D:\Programs\CMake\bin\cmake.exe -E rm -f CMakeFiles\RealCSVTest.dir/objects.a
D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\ar.exe qc CMakeFiles\RealCSVTest.dir/objects.a @CMakeFiles\RealCSVTest.dir\objects1.rsp
D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64\bin\g++.exe  -finput-charset=UTF-8 -O3 -DNDEBUG -Wl,--whole-archive CMakeFiles\RealCSVTest.dir/objects.a -Wl,--no-whole-archive -o ..\bin\RealCSVTest.exe -Wl,--out-implib,libRealCSVTest.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\RealCSVTest.dir\linklibs.rsp
