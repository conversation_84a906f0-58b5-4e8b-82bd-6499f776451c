#ifndef TESTDATAGENERATOR_H
#define TESTDATAGENERATOR_H

#include <QObject>
#include <QString>
#include <QStringList>
#include <QDateTime>
#include <QDir>
#include <QFile>
#include <QTextStream>
#include <QDebug>
#include <QRandomGenerator>

/**
 * @brief 测试数据生成器类
 * 
 * 负责生成各种测试场景的CSV数据文件，包括：
 * - 标准格式的正常测试数据
 * - 包含重复电机标签的数据
 * - 格式错误的异常数据
 * - 大数据量测试数据
 * - 特定异常场景数据
 */
class TestDataGenerator : public QObject
{
    Q_OBJECT

public:
    explicit TestDataGenerator(QObject *parent = nullptr);
    ~TestDataGenerator();

    // 异常类型枚举
    enum class ExceptionType {
        Normal,              // 正常数据
        DuplicateData,       // 重复数据
        InvalidFormat,       // 格式错误
        MissingColumns,      // 缺少列
        InvalidDataTypes,    // 数据类型错误
        FirmwareVersionMismatch, // 固件版本不匹配
        LargeDataSet,        // 大数据集
        EmptyFile,           // 空文件
        CorruptedData        // 损坏数据
    };

    // 主要生成方法
    QString generateNormalCSV(const QString& fileName, int recordCount = 10);
    QString generateDuplicateCSV(const QString& fileName, int duplicateCount = 3);
    QString generateInvalidCSV(const QString& fileName, ExceptionType type);
    QString generateLargeCSV(const QString& fileName, int recordCount = 1000);
    QString generateExceptionCSV(const QString& fileName, ExceptionType type);

    // 工具方法
    void setOutputDirectory(const QString& dir);
    QString getOutputDirectory() const;
    void clearGeneratedFiles();
    QStringList getGeneratedFiles() const;

    // 数据生成配置
    void setFirmwareVersion(const QString& version);
    void setDateTimeRange(const QDateTime& start, const QDateTime& end);
    void setAngleRange(float minAngle, float maxAngle);
    void setStandardAngleRange(float minStandard, float maxStandard);

signals:
    void fileGenerated(const QString& filePath);
    void generationProgress(int current, int total);
    void generationCompleted(const QString& summary);

private:
    // 私有辅助方法
    QString generateMotorLabel();
    QString generateMCUID();
    QString generateRandomFirmwareVersion();
    QString generateTimestamp();
    float generateRandomAngle(float min, float max);
    int generateTestResult();
    QString generateRemarks();

    // 数据写入方法
    bool writeCSVFile(const QString& filePath, const QStringList& headers, const QStringList& data);
    QString createCSVLine(const QStringList& fields);

    // 配置参数
    QString m_outputDirectory;
    QString m_firmwareVersion;
    QDateTime m_startDateTime;
    QDateTime m_endDateTime;
    float m_minAngle;
    float m_maxAngle;
    float m_minStandardAngle;
    float m_maxStandardAngle;

    // 状态管理
    QStringList m_generatedFiles;
    QStringList m_usedMotorLabels;  // 用于避免重复（除非特意生成重复数据）
};

#endif // TESTDATAGENERATOR_H
