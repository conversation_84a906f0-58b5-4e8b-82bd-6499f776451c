#ifndef DATASTORE_H
#define DATASTORE_H

#include <QSqlDatabase>
#include <Pojo/MESData.h>


enum SqlErrorType {
    NoError,                //无异常
    DataBaseOpenError,      //数据库打开异常
    MesDataNotFindError,    //MES找不到数据
    SqlCmdError,            //sql执行异常
    WorkOrderError,         //工单异常
    SNCodeError,            //sncode异常
    PreopNotFindError,      //上道工序找不到
    PreopNGError,           //上道工序不合格
    UnknownError            //未定义
};

class DataStore
{
public:
    DataStore();

    //打开数据库
    uint8_t open(QSqlDatabase &db);
    //关闭数据库
    uint8_t close(QSqlDatabase &db);
    //查询虚拟标签
    uint8_t getVirtualNbr(QSqlDatabase &db, QString mcuid, QString workOrder, QString &topNbr,QString domain);
    //关联标签
    uint8_t updataTopNbrForNbr(QSqlDatabase &db, const QString& nbr, const QString& topNbr, const QString& domain);
    //查询最大事务号
    uint8_t findXsubTrnbrMax(QSqlDatabase &db,QString xsub_n,uint32_t &trnbr, const QString &domain);//获取数据表中最大事务号;
    //检测底板标签信息
    uint8_t checkMesInfoForNbr(QSqlDatabase &db, QString& topNbr, QString nbr, QString domain, QString workOrder, QString& SNCode, QString snHardwareVersion, QString snFirewareVersion);
    //检测主板标签信息
    uint8_t checkMesInfoForTopNbr(QSqlDatabase &db,QString topNbr, QString preop, QString wordOrder);

    //上传测试数据
    uint8_t uploadMesData(QSqlDatabase &db, const MESData& data);

private:
    //生成insert语句
    QString createInsertSql(const MESData& data);
};

#endif // DATASTORE_H
