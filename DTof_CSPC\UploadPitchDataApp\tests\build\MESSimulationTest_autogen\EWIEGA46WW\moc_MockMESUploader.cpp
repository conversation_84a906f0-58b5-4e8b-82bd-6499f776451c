/****************************************************************************
** Meta object code from reading C++ file 'MockMESUploader.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../MockMESUploader.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'MockMESUploader.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_MockMESUploader_t {
    QByteArrayData data[23];
    char stringdata0[306];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_MockMESUploader_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_MockMESUploader_t qt_meta_stringdata_MockMESUploader = {
    {
QT_MOC_LITERAL(0, 0, 15), // "MockMESUploader"
QT_MOC_LITERAL(1, 16, 13), // "uploadStarted"
QT_MOC_LITERAL(2, 30, 0), // ""
QT_MOC_LITERAL(3, 31, 10), // "identifier"
QT_MOC_LITERAL(4, 42, 15), // "uploadCompleted"
QT_MOC_LITERAL(5, 58, 12), // "UploadResult"
QT_MOC_LITERAL(6, 71, 6), // "result"
QT_MOC_LITERAL(7, 78, 14), // "uploadProgress"
QT_MOC_LITERAL(8, 93, 7), // "current"
QT_MOC_LITERAL(9, 101, 5), // "total"
QT_MOC_LITERAL(10, 107, 17), // "statisticsUpdated"
QT_MOC_LITERAL(11, 125, 7), // "success"
QT_MOC_LITERAL(12, 133, 7), // "failure"
QT_MOC_LITERAL(13, 141, 20), // "batchUploadCompleted"
QT_MOC_LITERAL(14, 162, 10), // "totalCount"
QT_MOC_LITERAL(15, 173, 12), // "successCount"
QT_MOC_LITERAL(16, 186, 12), // "failureCount"
QT_MOC_LITERAL(17, 199, 18), // "networkInterrupted"
QT_MOC_LITERAL(18, 218, 22), // "databaseConnectionLost"
QT_MOC_LITERAL(19, 241, 13), // "uploadTimeout"
QT_MOC_LITERAL(20, 255, 13), // "onUploadTimer"
QT_MOC_LITERAL(21, 269, 17), // "onNetworkRecovery"
QT_MOC_LITERAL(22, 287, 18) // "onDatabaseRecovery"

    },
    "MockMESUploader\0uploadStarted\0\0"
    "identifier\0uploadCompleted\0UploadResult\0"
    "result\0uploadProgress\0current\0total\0"
    "statisticsUpdated\0success\0failure\0"
    "batchUploadCompleted\0totalCount\0"
    "successCount\0failureCount\0networkInterrupted\0"
    "databaseConnectionLost\0uploadTimeout\0"
    "onUploadTimer\0onNetworkRecovery\0"
    "onDatabaseRecovery"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_MockMESUploader[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      11,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       8,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   69,    2, 0x06 /* Public */,
       4,    2,   72,    2, 0x06 /* Public */,
       7,    2,   77,    2, 0x06 /* Public */,
      10,    3,   82,    2, 0x06 /* Public */,
      13,    3,   89,    2, 0x06 /* Public */,
      17,    0,   96,    2, 0x06 /* Public */,
      18,    0,   97,    2, 0x06 /* Public */,
      19,    1,   98,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      20,    0,  101,    2, 0x08 /* Private */,
      21,    0,  102,    2, 0x08 /* Private */,
      22,    0,  103,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString, 0x80000000 | 5,    3,    6,
    QMetaType::Void, QMetaType::Int, QMetaType::Int,    8,    9,
    QMetaType::Void, QMetaType::Int, QMetaType::Int, QMetaType::Int,    9,   11,   12,
    QMetaType::Void, QMetaType::Int, QMetaType::Int, QMetaType::Int,   14,   15,   16,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,    3,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void MockMESUploader::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<MockMESUploader *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->uploadStarted((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 1: _t->uploadCompleted((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< UploadResult(*)>(_a[2]))); break;
        case 2: _t->uploadProgress((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 3: _t->statisticsUpdated((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2])),(*reinterpret_cast< int(*)>(_a[3]))); break;
        case 4: _t->batchUploadCompleted((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2])),(*reinterpret_cast< int(*)>(_a[3]))); break;
        case 5: _t->networkInterrupted(); break;
        case 6: _t->databaseConnectionLost(); break;
        case 7: _t->uploadTimeout((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 8: _t->onUploadTimer(); break;
        case 9: _t->onNetworkRecovery(); break;
        case 10: _t->onDatabaseRecovery(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (MockMESUploader::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&MockMESUploader::uploadStarted)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (MockMESUploader::*)(const QString & , UploadResult );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&MockMESUploader::uploadCompleted)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (MockMESUploader::*)(int , int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&MockMESUploader::uploadProgress)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (MockMESUploader::*)(int , int , int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&MockMESUploader::statisticsUpdated)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (MockMESUploader::*)(int , int , int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&MockMESUploader::batchUploadCompleted)) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (MockMESUploader::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&MockMESUploader::networkInterrupted)) {
                *result = 5;
                return;
            }
        }
        {
            using _t = void (MockMESUploader::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&MockMESUploader::databaseConnectionLost)) {
                *result = 6;
                return;
            }
        }
        {
            using _t = void (MockMESUploader::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&MockMESUploader::uploadTimeout)) {
                *result = 7;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject MockMESUploader::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_MockMESUploader.data,
    qt_meta_data_MockMESUploader,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *MockMESUploader::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *MockMESUploader::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_MockMESUploader.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int MockMESUploader::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 11)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 11;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 11)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 11;
    }
    return _id;
}

// SIGNAL 0
void MockMESUploader::uploadStarted(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void MockMESUploader::uploadCompleted(const QString & _t1, UploadResult _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void MockMESUploader::uploadProgress(int _t1, int _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void MockMESUploader::statisticsUpdated(int _t1, int _t2, int _t3)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t3))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void MockMESUploader::batchUploadCompleted(int _t1, int _t2, int _t3)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t3))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}

// SIGNAL 5
void MockMESUploader::networkInterrupted()
{
    QMetaObject::activate(this, &staticMetaObject, 5, nullptr);
}

// SIGNAL 6
void MockMESUploader::databaseConnectionLost()
{
    QMetaObject::activate(this, &staticMetaObject, 6, nullptr);
}

// SIGNAL 7
void MockMESUploader::uploadTimeout(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 7, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
