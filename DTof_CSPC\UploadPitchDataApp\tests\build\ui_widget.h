/********************************************************************************
** Form generated from reading UI file 'widget.ui'
**
** Created by: Qt User Interface Compiler version 5.14.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_WIDGET_H
#define UI_WIDGET_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_Widget
{
public:
    QGridLayout *gridLayout;
    QPushButton *selectUploadFileBtn;
    QHBoxLayout *horizontalLayout;
    QLabel *uploadMESSize;
    QLabel *uploadMESSizeCnt;
    QHBoxLayout *horizontalLayout_5;
    QLabel *firmwareVersionErrSize;
    QLabel *firmwareVersionErrSizeCnt;
    QHBoxLayout *horizontalLayout_2;
    QLabel *mesInfoErrSize;
    QLabel *mesInfoErrSizeCnt;
    QHBoxLayout *horizontalLayout_3;
    QLabel *mesUploadErrSize;
    QLabel *mesUploadErrSizeCnt;
    QHBoxLayout *horizontalLayout_4;
    QLabel *mesUploadSuccessSize;
    QLabel *mesUploadSuccessSizeCnt;

    void setupUi(QWidget *Widget)
    {
        if (Widget->objectName().isEmpty())
            Widget->setObjectName(QString::fromUtf8("Widget"));
        Widget->resize(390, 221);
        gridLayout = new QGridLayout(Widget);
        gridLayout->setObjectName(QString::fromUtf8("gridLayout"));
        selectUploadFileBtn = new QPushButton(Widget);
        selectUploadFileBtn->setObjectName(QString::fromUtf8("selectUploadFileBtn"));

        gridLayout->addWidget(selectUploadFileBtn, 0, 0, 1, 1);

        horizontalLayout = new QHBoxLayout();
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        uploadMESSize = new QLabel(Widget);
        uploadMESSize->setObjectName(QString::fromUtf8("uploadMESSize"));

        horizontalLayout->addWidget(uploadMESSize);

        uploadMESSizeCnt = new QLabel(Widget);
        uploadMESSizeCnt->setObjectName(QString::fromUtf8("uploadMESSizeCnt"));

        horizontalLayout->addWidget(uploadMESSizeCnt);


        gridLayout->addLayout(horizontalLayout, 1, 0, 1, 1);

        horizontalLayout_5 = new QHBoxLayout();
        horizontalLayout_5->setObjectName(QString::fromUtf8("horizontalLayout_5"));
        firmwareVersionErrSize = new QLabel(Widget);
        firmwareVersionErrSize->setObjectName(QString::fromUtf8("firmwareVersionErrSize"));

        horizontalLayout_5->addWidget(firmwareVersionErrSize);

        firmwareVersionErrSizeCnt = new QLabel(Widget);
        firmwareVersionErrSizeCnt->setObjectName(QString::fromUtf8("firmwareVersionErrSizeCnt"));

        horizontalLayout_5->addWidget(firmwareVersionErrSizeCnt);


        gridLayout->addLayout(horizontalLayout_5, 2, 0, 1, 1);

        horizontalLayout_2 = new QHBoxLayout();
        horizontalLayout_2->setObjectName(QString::fromUtf8("horizontalLayout_2"));
        mesInfoErrSize = new QLabel(Widget);
        mesInfoErrSize->setObjectName(QString::fromUtf8("mesInfoErrSize"));

        horizontalLayout_2->addWidget(mesInfoErrSize);

        mesInfoErrSizeCnt = new QLabel(Widget);
        mesInfoErrSizeCnt->setObjectName(QString::fromUtf8("mesInfoErrSizeCnt"));

        horizontalLayout_2->addWidget(mesInfoErrSizeCnt);


        gridLayout->addLayout(horizontalLayout_2, 3, 0, 1, 1);

        horizontalLayout_3 = new QHBoxLayout();
        horizontalLayout_3->setObjectName(QString::fromUtf8("horizontalLayout_3"));
        mesUploadErrSize = new QLabel(Widget);
        mesUploadErrSize->setObjectName(QString::fromUtf8("mesUploadErrSize"));

        horizontalLayout_3->addWidget(mesUploadErrSize);

        mesUploadErrSizeCnt = new QLabel(Widget);
        mesUploadErrSizeCnt->setObjectName(QString::fromUtf8("mesUploadErrSizeCnt"));

        horizontalLayout_3->addWidget(mesUploadErrSizeCnt);


        gridLayout->addLayout(horizontalLayout_3, 4, 0, 1, 1);

        horizontalLayout_4 = new QHBoxLayout();
        horizontalLayout_4->setObjectName(QString::fromUtf8("horizontalLayout_4"));
        mesUploadSuccessSize = new QLabel(Widget);
        mesUploadSuccessSize->setObjectName(QString::fromUtf8("mesUploadSuccessSize"));

        horizontalLayout_4->addWidget(mesUploadSuccessSize);

        mesUploadSuccessSizeCnt = new QLabel(Widget);
        mesUploadSuccessSizeCnt->setObjectName(QString::fromUtf8("mesUploadSuccessSizeCnt"));

        horizontalLayout_4->addWidget(mesUploadSuccessSizeCnt);


        gridLayout->addLayout(horizontalLayout_4, 5, 0, 1, 1);


        retranslateUi(Widget);

        QMetaObject::connectSlotsByName(Widget);
    } // setupUi

    void retranslateUi(QWidget *Widget)
    {
        Widget->setWindowTitle(QCoreApplication::translate("Widget", "Widget", nullptr));
        selectUploadFileBtn->setText(QCoreApplication::translate("Widget", "\350\257\267\351\200\211\346\213\251\350\246\201\344\270\212\344\274\240\347\232\204\346\226\207\344\273\266", nullptr));
        uploadMESSize->setText(QCoreApplication::translate("Widget", "\351\234\200\350\246\201\344\270\212\344\274\240\346\225\260\346\215\256\346\225\260\351\207\217:", nullptr));
        uploadMESSizeCnt->setText(QCoreApplication::translate("Widget", "0", nullptr));
        firmwareVersionErrSize->setText(QCoreApplication::translate("Widget", "\345\233\272\344\273\266\347\211\210\346\234\254\345\274\202\345\270\270\346\225\260\351\207\217", nullptr));
        firmwareVersionErrSizeCnt->setText(QCoreApplication::translate("Widget", "0", nullptr));
        mesInfoErrSize->setText(QCoreApplication::translate("Widget", "MES\344\277\241\346\201\257\345\274\202\345\270\270\346\225\260\351\207\217:", nullptr));
        mesInfoErrSizeCnt->setText(QCoreApplication::translate("Widget", "0", nullptr));
        mesUploadErrSize->setText(QCoreApplication::translate("Widget", "MES\344\270\212\344\274\240\345\244\261\350\264\245\346\225\260\351\207\217:", nullptr));
        mesUploadErrSizeCnt->setText(QCoreApplication::translate("Widget", "0", nullptr));
        mesUploadSuccessSize->setText(QCoreApplication::translate("Widget", "MES\344\270\212\344\274\240\346\210\220\345\212\237\346\225\260\351\207\217:", nullptr));
        mesUploadSuccessSizeCnt->setText(QCoreApplication::translate("Widget", "0", nullptr));
    } // retranslateUi

};

namespace Ui {
    class Widget: public Ui_Widget {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_WIDGET_H
